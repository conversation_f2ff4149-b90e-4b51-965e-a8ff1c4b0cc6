import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:audioplayers/audioplayers.dart';

import 'dart:async';
import '../models/reaction_type.dart';

import 'dart:math';
import 'package:just_waveform/just_waveform.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class FeedVideoPlayer extends StatefulWidget {
  final String url;
  const FeedVideoPlayer({super.key, required this.url});

  @override
  State<FeedVideoPlayer> createState() => _FeedVideoPlayerState();
}

class _FeedVideoPlayerState extends State<FeedVideoPlayer> {
  late VideoPlayerController _videoController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _videoController = VideoPlayerController.network(widget.url);
    _videoController.initialize().then((_) {
      _chewieController = ChewieController(
        videoPlayerController: _videoController,
        autoPlay: true, // تشغيل تلقائي مثل Facebook
        looping: true, // تكرار الفيديو
        allowMuting: true,
        allowPlaybackSpeedChanging: false, // تبسيط الواجهة
        showControls: true, // إظهار الكونترولز في المنشورات
        playbackSpeeds: const [0.5, 1.0, 1.5, 2.0],
        optionsTranslation: OptionsTranslation(
          playbackSpeedButtonText: 'سرعة التشغيل',
          cancelButtonText: 'إلغاء',
        ),
        additionalOptions: (context) => [],
      );
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_chewieController == null || !_videoController.value.isInitialized) {
      return Container(
        height: 250,
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    // حساب نسبة العرض للفيديو
    final videoAspectRatio = _videoController.value.aspectRatio;
    final screenWidth = MediaQuery.of(context).size.width - 32; // مع الحواف

    // تحديد الارتفاع بناءً على نسبة العرض
    double videoHeight;
    if (videoAspectRatio > 1) {
      // فيديو أفقي - ارتفاع أقل
      videoHeight = screenWidth / videoAspectRatio;
      videoHeight = videoHeight.clamp(200.0, 300.0);
    } else {
      // فيديو عمودي - ارتفاع أكبر مثل Facebook
      videoHeight = screenWidth / videoAspectRatio;
      videoHeight = videoHeight.clamp(300.0, 500.0);
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: GestureDetector(
        onTap: () => _openFull(context),
        child: Container(
          height: videoHeight,
          width: double.infinity,
          color: Colors.black,
          child: Center(
            child: AspectRatio(
              aspectRatio: videoAspectRatio,
              child: Chewie(controller: _chewieController!),
            ),
          ),
        ),
      ),
    );
  }

  void _openFull(BuildContext context) {
    if (_chewieController == null) return;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              // الفيديو بملء الشاشة
              Positioned.fill(
                child: Center(
                  child: AspectRatio(
                    aspectRatio: _videoController.value.aspectRatio,
                    child: Chewie(controller: _chewieController!),
                  ),
                ),
              ),
              // زر الإغلاق
              Positioned(
                top: 40,
                right: 16,
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white, size: 30),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FeedImage extends StatelessWidget {
  final String url;
  const FeedImage({super.key, required this.url});

  void _openFull(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => _FullScreenImageViewer(url: url),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _openFull(context),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              url,
              fit: BoxFit.cover,
              height: 250,
              width: double.infinity,
              loadingBuilder: (context, child, progress) {
                if (progress == null) return child;
                return SizedBox(
                  height: 250,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: progress.expectedTotalBytes != null
                          ? progress.cumulativeBytesLoaded / progress.expectedTotalBytes!
                          : null,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stack) => const SizedBox(
                height: 250,
                child: Center(child: Icon(Icons.broken_image, size: 80)),
              ),
            ),
          ),
        ),
        Positioned(
          top: 8,
          left: 8,
          child: InkWell(
            onTap: () => _openFull(context),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black45,
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.fullscreen, color: Colors.white, size: 20),
            ),
          ),
        ),
      ],
    );
  }
}

class LinkPreview extends StatelessWidget {
  final String url;
  final Map<String, dynamic>? meta;
  const LinkPreview({super.key, required this.url, this.meta});

  @override
  Widget build(BuildContext context) {
    final title = meta?['title'] ?? url;
    final image = meta?['image'];
    final description = meta?['description'];
    return GestureDetector(
      onTap: () async {
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            if (image != null)
              ClipRRect(
                borderRadius: const BorderRadius.horizontal(left: Radius.circular(10)),
                child: Image.network(image, width: 100, height: 100, fit: BoxFit.cover),
              ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, maxLines: 2, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.bold)),
                    if (description != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(description, maxLines: 2, overflow: TextOverflow.ellipsis, style: TextStyle(color: Colors.grey[600])),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(url, maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(color: Colors.blue[700], fontSize: 12)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FeedAudioPlayer extends StatefulWidget {
  final String url;
  final bool isVoice;
  const FeedAudioPlayer({super.key, required this.url, this.isVoice = false});

  @override
  State<FeedAudioPlayer> createState() => _FeedAudioPlayerState();
}

class _FeedAudioPlayerState extends State<FeedAudioPlayer> with SingleTickerProviderStateMixin {
  late final AudioPlayer _player;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  double _speed = 1.0;

  final int _bars = 20;
  late final AnimationController _vizController;
  Waveform? _waveform;
  List<double>? _barSamples; // 0..1 amplitude per bar
  bool _waveLoading = false;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _player.setSourceUrl(widget.url);
    _player.onDurationChanged.listen((d) => setState(() => _duration = d));
    _player.onPositionChanged.listen((p) => setState(() => _position = p));
    _player.onPlayerComplete.listen((_) {
      _vizController.stop();
      _vizController.reset();
      setState(() {
        _isPlaying = false;
        _position = Duration.zero;
      });
    });
    _vizController = AnimationController(vsync: this, duration: const Duration(milliseconds: 800))
      ..addListener(() => setState(() {}));

    _prepareWaveform();
  }

  @override
  void dispose() {
    _player.dispose();
    _vizController.dispose();
    super.dispose();
  }

  void _toggle() async {
    if (_isPlaying) {
      await _player.pause();
    } else {
      await _player.resume();
    }
    setState(() => _isPlaying = !_isPlaying);

    if (_isPlaying) {
      _vizController.repeat();
    } else {
      _vizController.stop();
      _vizController.reset();
    }
  }

  void _changeSpeed() async {
    if (_speed == 1.0) {
      _speed = 1.5;
    } else if (_speed == 1.5) {
      _speed = 2.0;
    } else {
      _speed = 1.0;
    }
    await _player.setPlaybackRate(_speed);
    setState(() {});
  }

  Future<void> _prepareWaveform() async {
    if (_waveLoading) return;
    _waveLoading = true;
    try {
      final dir = await getTemporaryDirectory();
      final audioFile = File('${dir.path}/aud_${DateTime.now().millisecondsSinceEpoch}.tmp');

      // download
      final response = await http.get(Uri.parse(widget.url));
      if (response.statusCode == 200) {
        await audioFile.writeAsBytes(response.bodyBytes);
        final waveFile = File('${audioFile.path}.wave');
        final stream = JustWaveform.extract(
          audioInFile: audioFile,
          waveOutFile: waveFile,
        );
        await for (final progress in stream) {
          if (progress.waveform != null) {
            _waveform = progress.waveform;
            _computeBarSamples();
            break;
          }
        }
      }
    } catch (_) {
      // ignore errors, fallback to sine animation
    }
    if (mounted) setState(() {});
  }

  void _computeBarSamples() {
    if (_waveform == null) return;

    final totalMs = _waveform!.duration.inMilliseconds;
    final is16Bit = _waveform!.flags == 0; // 0 => 16-bit samples, else 8-bit
    final double denom = is16Bit ? 65535.0 : 255.0;

    _barSamples = List.generate(_bars, (i) {
      final double startFrac = i / _bars;
      final double endFrac = (i + 1) / _bars;

      final startPixel = _waveform!.positionToPixel(Duration(milliseconds: (totalMs * startFrac).floor())).toInt();
      final endPixel = _waveform!.positionToPixel(Duration(milliseconds: (totalMs * endFrac).ceil())).toInt();

      int maxAmp = 0;
      for (int p = startPixel; p <= endPixel; p++) {
        final amp = _waveform!.getPixelMax(p).abs();
        if (amp > maxAmp) maxAmp = amp;
      }

      return maxAmp / denom; // normalised 0..1
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isVoice
              ? [Colors.indigo.shade50, Colors.indigo.shade100]
              : [Colors.grey.shade200, Colors.grey.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 4, offset: const Offset(0, 2)),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(_isPlaying ? Icons.pause_circle : Icons.play_circle, size: 36, color: widget.isVoice ? Colors.indigo : Colors.blueGrey),
            onPressed: _toggle,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: widget.isVoice ? Colors.indigo : Colors.blueGrey,
                    inactiveTrackColor: (widget.isVoice ? Colors.indigo : Colors.blueGrey).withOpacity(0.3),
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                  ),
                  child: Slider(
                    value: _position.inMilliseconds.toDouble().clamp(0, _duration.inMilliseconds.toDouble()),
                    max: _duration.inMilliseconds.toDouble().clamp(1, double.infinity),
                    onChanged: (v) async {
                      final pos = Duration(milliseconds: v.toInt());
                      await _player.seek(pos);
                    },
                  ),
                ),
                SizedBox(
                  height: 20,
                  child: Row(
                    children: List.generate(_bars, (i) {
                      final progressFraction = _duration.inMilliseconds == 0 ? 0.0 : _position.inMilliseconds / _duration.inMilliseconds;
                      final activeIndex = (progressFraction * _bars).floor();

                      double amp = 0.2;
                      if (_barSamples != null && i < _barSamples!.length) {
                        amp = max(0.05, _barSamples![i]);
                      } else if (_isPlaying) {
                        // fallback sine if samples not ready
                        final phase = _vizController.value * 2 * pi;
                        amp = (sin(phase + i) + 1) / 2 * 0.8 + 0.2;
                      }

                      final double h = 4 + amp * 16;
                      final Color base = widget.isVoice ? Colors.indigo : Colors.blueGrey;
                      final Color barColor = i <= activeIndex ? base : base.withOpacity(0.4);

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 1.5),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 120),
                          width: 3,
                          height: h,
                          decoration: BoxDecoration(
                            color: barColor,
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                Text('${_format(_position)} / ${_format(_duration)}', style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
          TextButton(
            onPressed: _changeSpeed,
            child: Text('${_speed.toStringAsFixed(_speed == 2.0 ? 0 : 1)}x', style: TextStyle(color: widget.isVoice ? Colors.indigo : Colors.blueGrey)),
          ),
          if (widget.isVoice)
            const Padding(
              padding: EdgeInsets.only(left: 8.0),
              child: Icon(Icons.mic, size: 20, color: Colors.red),
            ),
        ],
      ),
    );
  }

  String _format(Duration d) {
    final m = d.inMinutes.remainder(60).toString().padLeft(2, '0');
    final s = d.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$m:$s';
  }
}

// عارض الصور بالشاشة الكاملة مع أزرار التفاعل
class _FullScreenImageViewer extends StatefulWidget {
  final String url;

  const _FullScreenImageViewer({required this.url});

  @override
  State<_FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<_FullScreenImageViewer> {
  bool _showControls = true;
  Timer? _hideControlsTimer;


  @override
  void initState() {
    super.initState();
    _startHideTimer();
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    super.dispose();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _startHideTimer();
    }
  }

  void _startHideTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // الصورة بالشاشة الكاملة
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleControls,
              child: Center(
                child: InteractiveViewer(
                  child: Image.network(widget.url),
                ),
              ),
            ),
          ),

          // أزرار التفاعل في الجانب الأيمن مثل TikTok (وسط الشاشة)
          if (_showControls)
            Positioned(
              right: 16,
              top: MediaQuery.of(context).size.height * 0.4,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // زر اللايك مع إمكانية إظهار كل التفاعلات عند النقر المستمر
                  _buildLikeButton(),
                  const SizedBox(height: 20),
                  // زر التعليقات
                  _buildActionButton(Icons.comment, '0', null),
                  const SizedBox(height: 20),
                  // زر المشاركة
                  _buildActionButton(Icons.share, '0', null),
                  const SizedBox(height: 20),
                  // زر المشاهدات
                  _buildActionButton(Icons.visibility, '0', null),
                ],
              ),
            ),

          // زر الإغلاق
          if (_showControls)
            Positioned(
              top: 40,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
              ),
            ),


        ],
      ),
    );
  }

  // بناء زر اللايك مع نفس طريقة المنشورات
  Widget _buildLikeButton() {
    return GestureDetector(
      onTap: () {
        // يمكن إضافة منطق اللايك هنا
      },
      onLongPressStart: (details) async {
        final selected = await _openReactionPickerAt(details.globalPosition);
        if (selected != null) {
          // يمكن إضافة منطق التفاعل هنا
        }
      },
      child: _buildActionButton(Icons.thumb_up_alt_rounded, '0', null),
    );
  }

  // نفس دالة إظهار التفاعلات من post_card.dart
  Future<ReactionType?> _openReactionPickerAt(Offset globalPosition) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.support,
      ReactionType.angry,
      ReactionType.sad,
    ];

    final left = globalPosition.dx - 150; // توسيط التفاعلات
    final top = globalPosition.dy - 60;

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          Positioned(
            left: left,
            top: top,
            child: _AnimatedReactionPickerForImage(
              reactions: items,
              onReactionSelected: (reaction) => Navigator.pop(ctx, reaction),
            ),
          ),
        ]);
      },
    );

    return selected;
  }

  // بناء أزرار الإجراءات مثل TikTok
  Widget _buildActionButton(IconData icon, String text, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 70,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 28),
            const SizedBox(height: 4),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// نفس كلاس التفاعلات المتحركة للصور
class _AnimatedReactionPickerForImage extends StatefulWidget {
  final List<ReactionType> reactions;
  final Function(ReactionType) onReactionSelected;

  const _AnimatedReactionPickerForImage({
    required this.reactions,
    required this.onReactionSelected,
  });

  @override
  State<_AnimatedReactionPickerForImage> createState() => _AnimatedReactionPickerForImageState();
}

class _AnimatedReactionPickerForImageState extends State<_AnimatedReactionPickerForImage>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;
  late AnimationController _containerController;
  late Animation<double> _containerScaleAnimation;

  @override
  void initState() {
    super.initState();

    // كونترولر للحاوية الرئيسية
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _containerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _containerController, curve: Curves.easeOutBack),
    );

    // كونترولرز للتفاعلات الفردية
    _controllers = List.generate(
      widget.reactions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 120),
        vsync: this,
      ),
    );

    // أنيميشن التكبير والتصغير
    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    // أنيميشن الانزلاق من الأسفل
    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutBack),
      );
    }).toList();

    // بدء الأنيميشن
    _startAnimations();
  }

  void _startAnimations() async {
    // بدء أنيميشن الحاوية
    _containerController.forward();

    // بدء أنيميشن التفاعلات واحدة تلو الأخرى
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      await Future.delayed(const Duration(milliseconds: 30));
    }
  }

  @override
  void dispose() {
    _containerController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _containerController,
      builder: (context, child) {
        return Transform.scale(
          scale: _containerScaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: widget.reactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reaction = entry.value;

                return AnimatedBuilder(
                  animation: _controllers[index],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimations[index].value,
                      child: SlideTransition(
                        position: _slideAnimations[index],
                        child: _buildReaction(reaction),
                      ),
                    );
                  },
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReaction(ReactionType reaction) {
    return GestureDetector(
      onTap: () => widget.onReactionSelected(reaction),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.white.withValues(alpha: 0.9),
                Colors.white.withValues(alpha: 0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Image.asset(
            reaction.assetPath,
            width: 28,
            height: 28,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}