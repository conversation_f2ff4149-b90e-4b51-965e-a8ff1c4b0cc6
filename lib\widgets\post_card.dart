import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'feed_media.dart';
import 'comments_sheet.dart';
import 'share_post_sheet.dart';
import 'link_preview.dart';
import 'reaction_details_sheet.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:readmore/readmore.dart';
import 'dart:async';

class PostCard extends StatefulWidget {
  final Post post;
  final VoidCallback? onRefresh;
  const PostCard({super.key, required this.post, this.onRefresh});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  late Post _post;
  bool _processing = false;
  bool _sentView = false;

  @override
  void initState() {
    super.initState();
    _post = widget.post;
  }

  Future<void> _react(ReactionType type) async {
    if (_processing) return;

    setState(() {
      Map<ReactionType, int> counts = Map.of(_post.reactionCounts);

      final bool wasPositive = _isPositive(_post.currentUserReaction);
      final bool willBePositive = _isPositive(type);

      if (_post.currentUserReaction == type) {
        // إزالة التفاعل (إلغاء)
        if (counts.containsKey(type)) {
          if (counts[type]! <= 1) {
            counts.remove(type);
          } else {
            counts[type] = counts[type]! - 1;
          }
        }
        _post = _post.copyWith(
          likesCount: wasPositive ? _post.likesCount - 1 : _post.likesCount,
          currentUserReaction: ReactionType.none,
          reactionCounts: counts,
        );
      } else {
        // إزالة تأثير التفاعل السابق إن وجد
        if (_post.currentUserReaction != ReactionType.none) {
          final old = _post.currentUserReaction;
          if (counts.containsKey(old)) {
            if (counts[old]! <= 1) {
              counts.remove(old);
            } else {
              counts[old] = counts[old]! - 1;
            }
          }
          if (wasPositive) {
            // كان سابقًا تفاعل إيجابى، نقص العدّاد
            _post = _post.copyWith(likesCount: _post.likesCount - 1);
          }
        }

        // أضف التفاعل الجديد
        counts[type] = (counts[type] ?? 0) + 1;

        _post = _post.copyWith(
          likesCount: (_post.currentUserReaction == ReactionType.none && willBePositive)
              ? _post.likesCount + 1
              : _post.likesCount + ((willBePositive ? 1 : 0) - (wasPositive ? 1 : 0)),
          currentUserReaction: type,
          reactionCounts: counts,
        );
      }
    });

    // Call backend without blocking UI
    _processing = true;
    SupabaseService()
        .toggleReaction(postId: _post.id, reaction: type)
        .whenComplete(() => setState(() => _processing = false));
  }

  Future<void> _openReactionPicker() async {
    // النقر العادي يعمل فقط كـ like عادي
    _react(ReactionType.like);
  }

  Future<ReactionType?> _openReactionPickerAt(Offset globalPos) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.support,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.angry,
      ReactionType.sad,
    ];

    const double iconWidth = 38; // حجم أصغر (32 حجم الإيموجى + حشوة صغيرة)
    final double barWidth = items.length * iconWidth + 16; // إضافة padding للحاوية
    const double barHeight = 44; // ارتفاع أصغر

    final media = MediaQuery.of(context);
    // تحريك الحاوية لليسار ضعفين
    double left = globalPos.dx - barWidth / 2 - (barWidth / 2);
    left = left.clamp(8.0, media.size.width - barWidth - 8.0);

    // جعل التفاعلات تظهر فوق الفاصل العلوي للأزرار (أعلى أكثر)
    double top = globalPos.dy - barHeight - 60; // أعلى بكثير من الإصبع لتظهر فوق الفاصل
    if (top < media.padding.top + 20) {
      top = media.padding.top + 20;
    }

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          Positioned(
            left: left,
            top: top,
            child: _AnimatedReactionPicker(
              reactions: items,
              onReactionSelected: (reaction) => Navigator.pop(ctx, reaction),
            ),
          ),
        ]);
      },
    );

    return selected;
  }

  void _openComments() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => CommentsSheet(
        post: _post,
        onCommentAdded: () async {
          // تحديث عدد التعليقات فوراً في الواجهة
          setState(() => _post = _post.copyWith(commentsCount: _post.commentsCount + 1));

          // إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
          _refreshCommentsCount();

          // تحديث الصفحة الرئيسية
          widget.onRefresh?.call();
        },
      ),
    );
  }

  /// إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
  Future<void> _refreshCommentsCount() async {
    try {
      final actualCount = await SupabaseService().getPostCommentsCount(_post.id);
      if (mounted && actualCount != _post.commentsCount) {
        setState(() {
          _post = _post.copyWith(commentsCount: actualCount);
        });
      }
    } catch (e) {
      // في حالة الخطأ، نحتفظ بالعدد الحالي
    }
  }

  void _openShareInternal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => SharePostSheet(post: _post, onShared: widget.onRefresh),
    );
  }

  void _shareToChat() async {
    final followers = await SupabaseService().fetchFollowingUsers();
    if (!mounted) return;
    showModalBottomSheet(
      context: context,
      builder: (_) => SafeArea(
        child: ListView.builder(
          itemCount: followers.length,
          itemBuilder: (ctx, i) {
            final f = followers[i];
            return ListTile(
              leading: CircleAvatar(backgroundImage: NetworkImage(f['avatar_url'] ?? '')),
              title: Text(f['name'] ?? 'مستخدم'),
              onTap: () async {
                Navigator.pop(ctx);
                final chatId = await SupabaseService().getOrCreateChat(f['id']);
                await SupabaseService().sendMessage(
                  chatId: chatId,
                  content: 'https://arzawo.com/posts/${_post.id}',
                );
                if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت مشاركة المنشور فى الدردشة')));
              },
            );
          },
        ),
      ),
    );
  }

  void _handleMenu(String value) async {
    switch (value) {
      case 'edit':
        final controller = TextEditingController(text: _post.content);
        final newText = await showDialog<String>(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('تعديل المنشور'),
            content: TextField(controller: controller, maxLines: 5),
            actions: [
              TextButton(onPressed: () => Navigator.pop(ctx), child: const Text('إلغاء')),
              TextButton(onPressed: () => Navigator.pop(ctx, controller.text.trim()), child: const Text('حفظ')),
            ],
          ),
        );
        if (newText != null && newText.isNotEmpty && newText != _post.content) {
          await SupabaseService().updatePostContent(_post.id, newText);
          widget.onRefresh?.call();
        }
        break;
      case 'delete':
        final ok = await showDialog<bool>(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('حذف المنشور؟'),
            content: const Text('لا يمكن التراجع بعد الحذف.'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
              TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
            ],
          ),
        );
        if (ok == true) {
          await SupabaseService().deletePost(_post.id);
          widget.onRefresh?.call();
        }
        break;
      case 'report':
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إرسال البلاغ')));
        break;
      case 'block':
        await SupabaseService().blockUser(_post.userId);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حظر المستخدم')));
        break;
      case 'toggle_save':
        await SupabaseService().toggleSavePost(_post.id);
        setState(() => _post = _post.copyWith(isSaved: !_post.isSaved));
        if (widget.onRefresh != null) widget.onRefresh!();
        final msg = _post.isSaved ? 'تمت الإزالة من المحفوظات' : 'تم الحفظ في المحفوظات';
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
        break;
      case 'privacy':
        final choice = await showDialog<String>(
          context: context,
          builder: (ctx) => SimpleDialog(
            title: const Text('من يمكنه رؤية هذا المنشور؟'),
            children: [
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'public'), child: const Text('الجميع')),
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'followers'), child: const Text('المتابِعون فقط')),
            ],
          ),
        );
        if (choice != null) {
          await SupabaseService().updatePostPrivacy(_post.id, choice);
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث الخصوصية')));
        }
        break;
      case 'comment_perm':
        final choice2 = await showDialog<String>(
          context: context,
          builder: (ctx) => SimpleDialog(
            title: const Text('من يمكنه التعليق؟'),
            children: [
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'everyone'), child: const Text('الجميع')),
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'followers'), child: const Text('المتابِعون فقط')),
            ],
          ),
        );
        if (choice2 != null) {
          await SupabaseService().updatePostCommentPermission(_post.id, choice2);
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث إعداد التعليقات')));
        }
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return VisibilityDetector(
      key: ValueKey('post_${_post.id}'),
      onVisibilityChanged: (info) {
        if (!_sentView && info.visibleFraction > 0.5) {
          _sentView = true;
          SupabaseService().incrementPostViews(_post.id);
          setState(() => _post = _post.copyWith(viewsCount: _post.viewsCount + 1));
        }
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(backgroundImage: NetworkImage(_post.userAvatar), radius: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(_post.userName, style: const TextStyle(fontWeight: FontWeight.bold)),
                        Text(_formatTime(_post.createdAt), style: theme.textTheme.bodySmall),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: _handleMenu,
                    itemBuilder: (_) {
                      final isMe = Supabase.instance.client.auth.currentUser?.id == _post.userId;
                      List<PopupMenuEntry<String>> items = [];
                      if (isMe) {
                        if (_post.type == PostType.text) {
                          items.add(_popupItem(
                            value: 'edit',
                            icon: Icons.edit,
                            label: 'تعديل المحتوى',
                            subtitle: 'تعديل نص المنشور الذى كتبته',
                          ));
                        }
                        items.add(_popupItem(
                          value: 'privacy',
                          icon: Icons.lock_outline,
                          label: 'تعديل الخصوصية',
                          subtitle: 'تحديد من يمكنه رؤية المنشور',
                        ));
                        items.add(_popupItem(
                          value: 'comment_perm',
                          icon: Icons.mode_comment_outlined,
                          label: 'من يمكنه التعليق؟',
                          subtitle: 'اختر من يستطيع إضافة تعليقات',
                        ));
                        items.add(_popupItem(
                          value: 'delete',
                          icon: Icons.delete_outline,
                          label: 'حذف',
                          subtitle: 'إزالة المنشور نهائياً',
                        ));
                      } else {
                        items.add(_popupItem(
                          value: 'report',
                          icon: Icons.flag_outlined,
                          label: 'الإبلاغ عن المنشور',
                          subtitle: 'سيتم مراجعته من الإدارة',
                        ));
                        items.add(_popupItem(
                          value: 'block',
                          icon: Icons.block,
                          label: 'حظر المستخدم',
                          subtitle: 'لن ترى محتوى هذا المستخدم بعد الآن',
                        ));
                      }

                      final saveLabel = _post.isSaved ? 'إزالة من المحفوظات' : (_post.type == PostType.video ? 'حفظ الفيديو' : 'حفظ المنشور');
                      final saveSubtitle = _post.isSaved ? 'سيتم الإزالة من قائمة المحفوظات' : 'سيتم حفظه فى قسم المحفوظات';
                      items.add(_popupItem(
                        value: 'toggle_save',
                        icon: _post.isSaved ? Icons.bookmark_remove_outlined : Icons.bookmark_add_outlined,
                        label: saveLabel,
                        subtitle: saveSubtitle,
                      ));
                      return items;
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (_post.bgColor != null && _post.type == PostType.text)
                Builder(builder: (context) {
                  final bgColor = Color(int.parse(_post.bgColor!.substring(1, 7), radix: 16) + 0xFF000000);
                  final textColor = ThemeData.estimateBrightnessForColor(bgColor) == Brightness.light
                      ? Colors.black
                      : Colors.white;
                  return Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 10),
                    decoration: BoxDecoration(
                      color: bgColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: ReadMoreText(
                      _post.content,
                      trimLines: 4,
                      trimMode: TrimMode.Line,
                      trimCollapsedText: ' عرض المزيد',
                      trimExpandedText: ' عرض أقل',
                      moreStyle: TextStyle(
                        color: textColor.withOpacity(0.85),
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                      lessStyle: TextStyle(
                        color: textColor.withOpacity(0.85),
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                      style: TextStyle(fontSize: 24, color: textColor, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  );
                })
              else if (_post.content.isNotEmpty)
                ReadMoreText(
                  _post.content,
                  trimLines: 4,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' عرض المزيد',
                  trimExpandedText: ' عرض أقل',
                  moreStyle: const TextStyle(color: Colors.blue),
                  lessStyle: const TextStyle(color: Colors.blue),
                ),
              if (_post.type == PostType.image && _post.mediaUrl != null) ...[
                const SizedBox(height: 8),
                FeedImage(url: _post.mediaUrl!),
              ] else if (_post.type == PostType.video && _post.mediaUrl != null) ...[
                const SizedBox(height: 8),
                FeedVideoPlayer(url: _post.mediaUrl!),
              ] else if ((_post.type == PostType.audio || _post.type == PostType.voice) && _post.mediaUrl != null) ...[
                const SizedBox(height: 8),
                FeedAudioPlayer(url: _post.mediaUrl!, isVoice: _post.type == PostType.voice),
              ] else if (_post.type == PostType.link && _post.linkUrl != null) ...[
                const SizedBox(height: 8),
                LinkPreview(url: _post.linkUrl!, meta: _post.linkMeta),
              ],
              const SizedBox(height: 8),
              // سطر واحد يحتوي على الأيقونات المتداخلة والإحصائيات
              _buildStatsRow(),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    _fbAction(
                      icon: _reactionIconSmall(),
                      label: _reactionLabel(),
                      count: _post.likesCount,
                      onTap: () {
                        if (_post.currentUserReaction == ReactionType.none || _post.currentUserReaction == ReactionType.like) {
                          _react(ReactionType.like);
                        } else {
                          _openReactionPicker();
                        }
                      },
                      onLongPressStart: (details) async {
                        final selected = await _openReactionPickerAt(details.globalPosition);
                        if (selected != null) {
                          _react(selected);
                        }
                      },
                    ),
                    _fbAction(
                      icon: const Icon(Icons.comment_outlined, size: 18),
                      label: 'تعليق',
                      count: _post.commentsCount,
                      onTap: _openComments,
                    ),
                    _fbAction(
                      icon: const Icon(Icons.share_outlined, size: 18),
                      label: 'مشاركة',
                      count: _post.sharesCount,
                      onTap: _openShareInternal,
                    ),
                    _fbAction(
                      icon: const Icon(Icons.remove_red_eye_outlined, size: 18),
                      label: 'مشاهدة',
                      count: _post.viewsCount,
                      onTap: null,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _fbAction({
    required Widget icon,
    required String label,
    required int count,
    VoidCallback? onTap,
    GestureLongPressStartCallback? onLongPressStart,
  }) {
    final color = Colors.grey.shade600;
    final content = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        icon,
        const SizedBox(width: 4),
        Text(label, style: TextStyle(fontSize: 12, color: color)),
      ],
    );

    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        onLongPressStart: onLongPressStart,
        child: content,
      ),
    );
  }

  Widget _reactionIconSmall() {
    if (_post.currentUserReaction == ReactionType.none) {
      return const Icon(Icons.thumb_up_alt_outlined, size: 18);
    }
    return Image.asset(
      _post.currentUserReaction.assetPath,
      width: 18,
      height: 18,
      fit: BoxFit.contain,
    );
  }

  String _reactionLabel() {
    if (_post.currentUserReaction == ReactionType.none || _post.currentUserReaction == ReactionType.like) {
      return 'أعجبني';
    }
    // Arabic labels mapping
    switch (_post.currentUserReaction) {
      case ReactionType.dislike:
        return 'لا يعجبني';
      case ReactionType.celebrate:
        return 'مبروك';
      case ReactionType.support:
        return 'أدعمك';
      case ReactionType.love:
        return 'أحببته';
      case ReactionType.funny:
        return 'أضحكني';
      case ReactionType.angry:
        return 'أغضبني';
      case ReactionType.sad:
        return 'أحزنني';
      default:
        return 'أعجبني';
    }
  }

  String _formatTime(DateTime dt) {
    final diff = DateTime.now().difference(dt);
    if (diff.inSeconds < 60) return 'الآن';
    if (diff.inMinutes < 60) return 'قبل ${diff.inMinutes} دقيقة';
    if (diff.inHours < 24) return 'قبل ${diff.inHours} ساعة';
    if (diff.inDays < 7) return 'قبل ${diff.inDays} يوم';
    return '${dt.day}/${dt.month}/${dt.year}';
  }

  PopupMenuItem<String> _popupItem({required String value, required IconData icon, required String label, required String subtitle}) {
    return PopupMenuItem(
      value: value,
      child: Row(
        children: [
          Icon(icon, color: Colors.black87),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black87)),
                const SizedBox(height: 2),
                Text(subtitle, style: const TextStyle(fontSize: 11, color: Colors.grey)),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildStatsRow() {
    // بناء الأيقونات المتداخلة
    final entries = _post.reactionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final iconWidgets = <Widget>[];
    // عرض 3 تفاعلات كحد أقصى مع تداخل مثل فيسبوك
    for (int i = 0; i < entries.length && i < 3; i++) {
      final rt = entries[i].key;
      iconWidgets.add(Container(
        margin: EdgeInsets.only(right: i == 0 ? 0 : 2), // مسافة صغيرة بين الأيقونات بدلاً من التداخل
        child: ClipOval(
          child: Image.asset(
            rt.assetPath,
            width: 20,
            height: 20,
            fit: BoxFit.contain,
          ),
        ),
      ));
    }

    final total = _post.reactionCounts.values.fold<int>(0, (a, b) => a + b);

    // بناء الإحصائيات
    final stats = <String>[];

    if (_post.commentsCount > 0) {
      stats.add('${_post.commentsCount} تعليق');
    }

    if (_post.sharesCount > 0) {
      stats.add('${_post.sharesCount} مشاركة');
    }

    if (_post.viewsCount > 0) {
      stats.add('${_post.viewsCount} مشاهدة');
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // إحصائيات التفاعلات (العدد + الأيقونات المتداخلة) - تبديل مواقع الرقم والأيقونات
        if (_post.reactionCounts.isNotEmpty)
          GestureDetector(
            onTap: () => _showReactionDetails(),
            child: Row(
              children: [
                Text('$total', style: const TextStyle(fontSize: 13, fontWeight: FontWeight.bold)),
                const SizedBox(width: 8),
                Stack(
                  children: iconWidgets.reversed.toList(),
                ),
              ],
            ),
          )
        else
          const SizedBox.shrink(),

        // إحصائيات الأزرار الأخرى (التعليقات، المشاركات، المشاهدات) في اليمين
        if (stats.isNotEmpty)
          Text(
            stats.join(' • '),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          )
        else
          const SizedBox.shrink(),
      ],
    );
  }

  void _showReactionDetails() async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // جلب تفاصيل المتفاعلين من الخادم
      final reactionDetails = await SupabaseService().getPostReactionDetails(_post.id);

      if (!mounted) return;

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // عرض النافذة المنبثقة
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => ReactionDetailsSheet(
          reactionDetails: reactionDetails,
          reactionCounts: _post.reactionCounts,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // إغلاق مؤشر التحميل في حالة الخطأ
      Navigator.of(context).pop();

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في جلب تفاصيل التفاعلات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  bool _isPositive(ReactionType t) => [
        ReactionType.like,
        ReactionType.celebrate,
        ReactionType.support,
        ReactionType.love,
        ReactionType.insightful,
        ReactionType.funny,
      ].contains(t);
}

class _AnimatedReactionPicker extends StatefulWidget {
  final List<ReactionType> reactions;
  final Function(ReactionType) onReactionSelected;

  const _AnimatedReactionPicker({
    required this.reactions,
    required this.onReactionSelected,
  });

  @override
  State<_AnimatedReactionPicker> createState() => _AnimatedReactionPickerState();
}

class _AnimatedReactionPickerState extends State<_AnimatedReactionPicker>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _bounceAnimations;
  late List<Animation<double>> _shimmerAnimations;
  late AnimationController _containerController;
  late Animation<double> _containerScaleAnimation;

  @override
  void initState() {
    super.initState();

    // كونترولر للحاوية الرئيسية
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _containerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _containerController, curve: Curves.easeOutBack),
    );

    // كونترولرز للتفاعلات الفردية - أسرع بكثير
    _controllers = List.generate(
      widget.reactions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 120), // أسرع جداً مثل Facebook
        vsync: this,
      ),
    );

    // أنيميشن التكبير مع تأثير مرتد قوي
    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    // أنيميشن الانزلاق من الأسفل
    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(begin: const Offset(0, 1.0), end: Offset.zero).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    // أنيميشن الارتداد المستمر
    _bounceAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // أنيميشن اللمعان
    _shimmerAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // بدء الأنيميشن فوراً وبسرعة
    _startFastSequentialAnimation();
  }

  void _startFastSequentialAnimation() async {
    // بدء أنيميشن الحاوية أولاً
    _containerController.forward();

    // بدء التفاعلات بسرعة فائقة مثل Facebook
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      // تأخير قصير جداً بين الأيقونات لتأثير متتالي سريع
      await Future.delayed(const Duration(milliseconds: 25));
    }
  }



  @override
  void dispose() {
    _containerController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: widget.reactions.asMap().entries.map((entry) {
            final index = entry.key;
            final reaction = entry.value;

            return AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimations[index].value,
                  child: SlideTransition(
                    position: _slideAnimations[index],
                    child: _buildShimmeringReaction(reaction, index),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildShimmeringReaction(ReactionType reaction, int index) {
    return GestureDetector(
      onTap: () => widget.onReactionSelected(reaction),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Stack(
          children: [
            // الأيقونة الأساسية مع تأثير لامع
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.9),
                    Colors.white.withValues(alpha: 0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Image.asset(
                reaction.assetPath,
                width: 28,
                height: 28,
                fit: BoxFit.contain,
              ),
            ),
            // تأثير اللمعان المتحرك
            AnimatedBuilder(
              animation: _shimmerAnimations[index],
              builder: (context, child) {
                return Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withValues(alpha: 0.0),
                          Colors.white.withValues(alpha: 0.7 * _shimmerAnimations[index].value),
                          Colors.white.withValues(alpha: 0.0),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                        begin: Alignment(-1.0 + 2.0 * _shimmerAnimations[index].value, -1.0),
                        end: Alignment(1.0 + 2.0 * _shimmerAnimations[index].value, 1.0),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}