import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/comments_sheet.dart';
import '../widgets/share_post_sheet.dart';

class VideosPage extends StatefulWidget {
  const VideosPage({super.key});

  @override
  State<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends State<VideosPage> {
  final PageController _pageController = PageController();
  int _current = 0;

  List<Post> _videos = [];
  bool _loading = true;
  RealtimeChannel? _channel;

  @override
  void initState() {
    super.initState();
    _loadVideos();
    _subscribeRealtime();
  }

  void _loadVideos() async {
    final list = await SupabaseService().fetchPosts();
    final vids = list.where((p) => p.type == PostType.video && p.mediaUrl != null).toList();
    if (mounted) {
      setState(() {
        _videos = vids;
        _loading = false;
      });
    }
  }

  void _subscribeRealtime() {
    final client = Supabase.instance.client;
    _channel = client.channel('public:posts');

    _channel!.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: 'posts',
      callback: (payload, [ref]) {
        final newRow = payload.newRecord as Map<String, dynamic>?;
        if (newRow != null && newRow['type'] == 'video') {
          _loadVideos();
        }
      },
    );

    _channel!.subscribe();
  }

  @override
  void dispose() {
    _pageController.dispose();
    if (_channel != null) {
      Supabase.instance.client.removeChannel(_channel!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_videos.isEmpty) {
      return const Center(child: Text('لا توجد فيديوهات بعد'));
    }
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: _videos.length,
      onPageChanged: (i) => setState(() => _current = i),
      itemBuilder: (context, index) {
        final post = _videos[index];
        return _VideoItem(
          post: post,
          isActive: index == _current,
          onFinished: () {
            if (index < _videos.length - 1) {
              _pageController.animateToPage(index + 1, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
            }
          },
        );
      },
    );
  }
}

class _VideoItem extends StatefulWidget {
  final Post post;
  final bool isActive;
  final VoidCallback onFinished;
  const _VideoItem({required this.post, required this.isActive, required this.onFinished});

  @override
  State<_VideoItem> createState() => _VideoItemState();
}

class _VideoItemState extends State<_VideoItem> with AutomaticKeepAliveClientMixin {
  late VideoPlayerController _videoController;
  ChewieController? _chewie;
  bool _isFollowing = false;
  bool _loadingFollow = true;
  bool _triggeredNext = false;

  // Local mutable copy to reflect live counts
  late Post _post;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _post = widget.post;
    _videoController = VideoPlayerController.network(widget.post.mediaUrl!);
    _videoController.initialize().then((_) => setState(() {}));
    _chewie = ChewieController(
      videoPlayerController: _videoController,
      autoPlay: widget.isActive,
      looping: false,
      showControls: true,
      allowMuting: true,
      allowPlaybackSpeedChanging: true,
      playbackSpeeds: const [0.5, 1.0, 1.5, 2.0],
      optionsTranslation: OptionsTranslation(
        playbackSpeedButtonText: 'سرعة التشغيل',
        cancelButtonText: 'إلغاء',
      ),
    );

    SupabaseService().isFollowing(widget.post.userId).then((res) {
      if (mounted) setState(() {
        _isFollowing = res;
        _loadingFollow = false;
      });
    });

    _videoController.addListener(() {
      if (widget.isActive) {
        // Reset flag when video rewinds / becomes active again
        if (!_videoController.value.isPlaying && _videoController.value.position == Duration.zero) {
          _triggeredNext = false;
        }

        final position = _videoController.value.position;
        final duration = _videoController.value.duration;
        if (duration != null && duration.inMilliseconds > 0) {
          final remaining = duration - position;

          // إذا عاد المستخدم بالزمن أو ما زال أمامه أكثر من 0.3 ثانية (الفيديو لم ينته بعد)
          if (remaining > const Duration(milliseconds: 300)) {
            _triggeredNext = false; // السماح بالتمرير مرة أخرى عند الاقتراب من نهاية الفيديو
          }

          // مرّر تلقائياً فقط عند الانتهاء (باقى ≤ 0.3 ثانية)
          if (!_triggeredNext && remaining <= const Duration(milliseconds: 300)) {
            _triggeredNext = true;
            widget.onFinished();
          }
        }
      } else {
        _triggeredNext = false; // reset when item becomes inactive
      }
    });
  }

  @override
  void didUpdateWidget(covariant _VideoItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // When item becomes active again (user رجع للخلف)
    if (widget.isActive && !oldWidget.isActive) {
      // أعد الفيديو للبداية وأعد ضبط العلَم
      _videoController.seekTo(Duration.zero);
      _triggeredNext = false;
    }

    if (widget.isActive && !_videoController.value.isPlaying) {
      _videoController.play();
    } else if (!widget.isActive && _videoController.value.isPlaying) {
      _videoController.pause();
    }
  }

  @override
  void dispose() {
    _chewie?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (!_videoController.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // حساب نسبة العرض للفيديو
    final videoAspectRatio = _videoController.value.aspectRatio;
    final screenSize = MediaQuery.of(context).size;
    final screenAspectRatio = screenSize.width / screenSize.height;

    return Stack(
      children: [
        // عرض الفيديو بملء الشاشة مثل Facebook
        Positioned.fill(
          child: Container(
            color: Colors.black,
            child: Center(
              child: AspectRatio(
                aspectRatio: videoAspectRatio,
                child: SizedBox(
                  width: screenSize.width,
                  height: videoAspectRatio > screenAspectRatio
                      ? screenSize.width / videoAspectRatio  // فيديو عمودي
                      : screenSize.height,  // فيديو أفقي
                  child: Chewie(controller: _chewie!),
                ),
              ),
            ),
          ),
        ),

        // معلومات المستخدم وزر المتابعة
        Positioned(
          top: 40,
          right: 16,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(backgroundImage: NetworkImage(widget.post.userAvatar), radius: 18),
              const SizedBox(width: 8),
              Text(widget.post.userName, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              _loadingFollow
                  ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                  : TextButton(
                      onPressed: () async {
                        setState(() => _loadingFollow = true);
                        final nowFollow = await SupabaseService().toggleFollow(widget.post.userId);
                        if (mounted) setState(() {
                          _isFollowing = nowFollow;
                          _loadingFollow = false;
                        });
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: _isFollowing ? Colors.grey.withOpacity(0.6) : Colors.red,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      child: Text(
                        _isFollowing ? 'متابَع' : 'متابعة',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
            ],
          ),
        ),

        // وصف الفيديو أسفل
        if (widget.post.content.isNotEmpty)
          Positioned(
            bottom: 70,
            right: 16,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.7,
              child: Text(widget.post.content, style: const TextStyle(color: Colors.white), maxLines: 3, overflow: TextOverflow.ellipsis),
            ),
          ),

        // أزرار الإعجاب والتعليق والمشاركة – على الجانب الأيمن مثل تيك توك
        Positioned(
          right: 8,
          bottom: 120,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _actionButton(
                icon: Icons.favorite,
                active: _post.currentUserReaction == ReactionType.like,
                activeColor: Colors.red,
                count: _post.likesCount,
                onTap: () => _react(ReactionType.like),
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.comment,
                active: false,
                count: _post.commentsCount,
                onTap: _openComments,
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.share,
                active: false,
                count: _post.sharesCount,
                onTap: _sharePost,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButton({required IconData icon, required bool active, required int count, required VoidCallback onTap, Color? activeColor}) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black45,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: active ? (activeColor ?? Colors.white) : Colors.white, size: 28),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$count',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }

  Future<void> _react(ReactionType type) async {
    // Optimistic UI update
    setState(() {
      if (type == ReactionType.like) {
        if (_post.currentUserReaction == ReactionType.like) {
          _post = _post.copyWith(
            currentUserReaction: ReactionType.none,
            likesCount: _post.likesCount - 1,
          );
        } else {
          // لا يوجد تفاعل سلبى الآن
          _post = _post.copyWith(
            currentUserReaction: ReactionType.like,
            likesCount: _post.likesCount + 1,
          );
        }
      }
    });

    await SupabaseService().toggleReaction(postId: _post.id, reaction: type);
  }

  void _openComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => CommentsSheet(
        post: _post,
        onCommentAdded: () async {
          // تحديث عدد التعليقات فوراً في الواجهة
          setState(() => _post = _post.copyWith(commentsCount: _post.commentsCount + 1));

          // إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
          try {
            final actualCount = await SupabaseService().getPostCommentsCount(_post.id);
            if (mounted && actualCount != _post.commentsCount) {
              setState(() {
                _post = _post.copyWith(commentsCount: actualCount);
              });
            }
          } catch (e) {
            // في حالة الخطأ، نحتفظ بالعدد الحالي
          }
        },
      ),
    );
  }

  void _sharePost() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => SharePostSheet(post: _post, onShared: () {
        setState(() => _post = _post.copyWith(sharesCount: _post.sharesCount + 1));
      }),
    );
  }
} 